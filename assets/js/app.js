// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//
// If you have dependencies that try to import CSS, esbuild will generate a separate `app.css` file.
// To load it, simply add a second `<link>` to your `root.html.heex` file.

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"
import Sortable from "sortablejs"
import { gsap } from "gsap"
import { ScrollToPlugin } from "gsap/ScrollToPlugin"
import "@alenaksu/json-viewer"
import { SlickGrid, SlickDataView, SlickGroupItemMetadataProvider } from "slickgrid"
import { PayloadEditor, UnifiedPayloadEditor } from "./payload_editor"
import FileUpload from "./file_upload_hook"

// Register GSAP plugins
gsap.registerPlugin(ScrollToPlugin)

const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks
const Hooks = {

  PayloadEditor: PayloadEditor,
  UnifiedPayloadEditor: UnifiedPayloadEditor,
  FileUpload: FileUpload,

  BrokerTabsSortable: {
    mounted() {
      const container = this.el;

      // Initialize Sortable for broker tabs
      this.sortable = new Sortable(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',

        // Only allow dragging of broker tabs, not buttons or close buttons
        filter: 'button, .add-broker-btn',
        preventOnFilter: true,

        // Prevent dragging when clicking on close buttons
        onStart: (evt) => {
          // Check if the click target is a close button or its child
          const target = evt.originalEvent.target;
          const closeButton = target.closest('span[phx-click="close_broker_tab"]');
          if (closeButton) {
            // Cancel the drag operation
            return false;
          }
        },

        // Event triggered when sorting is stopped
        onEnd: (evt) => {
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;

          // Only push the event if the order actually changed
          if (oldIndex !== newIndex) {
            // Send the reordering event to the server
            this.pushEvent('reorder_broker_tabs', {
              old_index: oldIndex,
              new_index: newIndex
            });
          }
        }
      });

      // Handle close button clicks manually to ensure they work
      this.handleCloseButtonClick = (event) => {
        const closeButton = event.target.closest('span[phx-click="show_delete_confirm"]');
        if (closeButton) {
          event.stopPropagation();
          event.preventDefault();

          // Get the broker name from the phx-value-name attribute
          const brokerName = closeButton.getAttribute('phx-value-name');
          const target = closeButton.getAttribute('phx-target');

          if (brokerName && target) {
            // Send the show delete confirm event to the specific component target
            this.pushEventTo(target, 'show_delete_confirm', { name: brokerName });
          }
        }
      };

      // Add click event listener to the container for close buttons
      container.addEventListener('click', this.handleCloseButtonClick);
    },

    destroyed() {
      // Clean up event listeners
      if (this.handleCloseButtonClick) {
        this.el.removeEventListener('click', this.handleCloseButtonClick);
      }

      // Clean up Sortable instance when the element is removed
      if (this.sortable) {
        this.sortable.destroy();
      }
    }
  },

  // Hook for handling protocol changes in connection set forms
  ProtocolSelector: {
    mounted() {
      this.handleProtocolChange = () => {
        const protocol = this.el.value;
        const sslContainer = document.getElementById('ssl-section-container');
        const websocketPathContainer = document.getElementById('websocket-path-section');
        const portInput = document.querySelector('input[name="connection_set[port]"]');
        const wsPathInput = document.querySelector('input[name="connection_set[ws_path]"]');

        // Show SSL section for secure protocols
        if (sslContainer) {
          if (['mqtts', 'wss', 'quic'].includes(protocol)) {
            sslContainer.style.display = 'block';
          } else {
            sslContainer.style.display = 'none';
          }
        }

        // Show WebSocket path section for WebSocket protocols
        if (websocketPathContainer) {
          if (['ws', 'wss'].includes(protocol)) {
            websocketPathContainer.style.display = 'block';
            // Set default WebSocket path if empty
            if (wsPathInput && !wsPathInput.value) {
              wsPathInput.value = '/mqtt';
            }
          } else {
            websocketPathContainer.style.display = 'none';
          }
        }

        // Update port field based on protocol
        if (portInput) {
          // Only update port if it's a default port value
          const defaultPorts = {
            mqtt: "1883",
            mqtts: "8883",
            ws: "8083",
            wss: "8084",
            quic: "14567"
          };

          // Check if current port is one of the default ports
          const isDefaultPort = Object.values(defaultPorts).includes(portInput.value);

          // Only update if it's a default port to avoid overwriting custom ports
          if (isDefaultPort) {
            portInput.value = defaultPorts[protocol] || "1883";
          }
        }
      };

      // Set initial state
      this.handleProtocolChange();

      // Add event listener for changes
      this.el.addEventListener('change', this.handleProtocolChange);
    },

    destroyed() {
      // Clean up event listener
      if (this.handleProtocolChange) {
        this.el.removeEventListener('change', this.handleProtocolChange);
      }
    }
  },

  // We're now using the onchange attribute directly on the radio buttons
  // instead of a hook for certificate type selection

  // We've removed the ConnectionStatusSortable and ConnectionStatusContainer hooks
  // as we've replaced the component with a table view

  // Hook for handling connection table animations with GSAP
  ConnectionTableAutoAnimate: {
    mounted() {
      this.setupGSAPAnimations();
      this.observeTableChanges();
    },

    updated() {
      // Detect reordering and animate accordingly
      this.handleReordering();
      // Re-observe table changes when component updates
      this.observeTableChanges();
    },

    setupGSAPAnimations() {
      // Set up GSAP defaults for smooth animations
      gsap.defaults({
        duration: 0.4,
        ease: "power2.out"
      });

      // Store reference to table body for animations
      this.tableBody = this.el.querySelector('tbody');
      this.previousRows = new Map();
      this.isReordering = false;

      if (this.tableBody) {
        // Store initial state of rows
        this.captureRowState();
      }
    },

    observeTableChanges() {
      if (!this.tableBody) return;

      // Create a mutation observer to detect row changes
      if (this.observer) {
        this.observer.disconnect();
      }

      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            this.handleRowChanges(mutation);
          }
        });
      });

      this.observer.observe(this.tableBody, {
        childList: true,
        subtree: true
      });

      // Also observe status changes within existing rows
      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      this.statusObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            this.handleStatusChange(mutation.target);
          }
        });
      });

      // Observe all status elements for changes
      const statusElements = this.tableBody.querySelectorAll('.swap input[type="checkbox"]');
      statusElements.forEach(element => {
        this.statusObserver.observe(element, {
          attributes: true,
          attributeFilter: ['checked']
        });
      });
    },

    handleReordering() {
      if (!this.tableBody || this.isReordering) return;

      const currentRows = this.tableBody.querySelectorAll('tr');
      const currentOrder = Array.from(currentRows).map(row => this.getRowClientId(row)).filter(Boolean);
      const previousOrder = Array.from(this.previousRows.keys());

      // Check if this is a reordering (same items, different order)
      if (this.isReorderingDetected(currentOrder, previousOrder)) {
        this.animateReordering(currentRows);
      }

      // Update state after potential reordering
      setTimeout(() => this.captureRowState(), 50);
    },

    isReorderingDetected(currentOrder, previousOrder) {
      // Check if we have the same items but in different order
      if (currentOrder.length !== previousOrder.length) return false;

      const currentSet = new Set(currentOrder);
      const previousSet = new Set(previousOrder);

      // Same items but different order
      return currentSet.size === previousSet.size &&
             [...currentSet].every(item => previousSet.has(item)) &&
             !currentOrder.every((item, index) => item === previousOrder[index]);
    },

    animateReordering(currentRows) {
      this.isReordering = true;

      // FLIP animation technique for smooth reordering
      const animations = [];

      currentRows.forEach((row) => {
        const clientId = this.getRowClientId(row);
        if (!clientId || !this.previousRows.has(clientId)) return;

        const previousData = this.previousRows.get(clientId);
        const currentRect = row.getBoundingClientRect();
        const previousRect = previousData.rect;

        // Calculate the difference (FLIP: First, Last, Invert, Play)
        const deltaY = previousRect.top - currentRect.top;
        const deltaX = previousRect.left - currentRect.left;

        if (Math.abs(deltaY) > 1 || Math.abs(deltaX) > 1) {
          // Set initial position (Invert)
          gsap.set(row, {
            y: deltaY,
            x: deltaX
          });

          // Animate to final position (Play)
          const animation = gsap.to(row, {
            duration: 0.6,
            y: 0,
            x: 0,
            ease: "power2.out",
            onComplete: () => {
              // Clear transforms
              gsap.set(row, { clearProps: "all" });
            }
          });

          animations.push(animation);
        }
      });

      // Add stagger effect for visual appeal
      if (animations.length > 0) {
        gsap.to(currentRows, {
          duration: 0.1,
          scale: 1.02,
          yoyo: true,
          repeat: 1,
          stagger: 0.03,
          ease: "power2.inOut",
          onComplete: () => {
            this.isReordering = false;
          }
        });
      } else {
        this.isReordering = false;
      }
    },

    captureRowState() {
      if (!this.tableBody) return;

      const rows = this.tableBody.querySelectorAll('tr');
      this.previousRows.clear();

      rows.forEach((row, index) => {
        const clientId = this.getRowClientId(row);
        if (clientId) {
          this.previousRows.set(clientId, {
            element: row,
            index: index,
            rect: row.getBoundingClientRect()
          });
        }
      });
    },

    getRowClientId(row) {
      // Extract client ID from the row's data or content
      const clientIdCell = row.querySelector('td:nth-child(2) span');
      return clientIdCell ? clientIdCell.textContent.trim() : null;
    },

    handleRowChanges(mutation) {
      // Skip animations if we're currently reordering
      if (this.isReordering) return;

      // Handle removed nodes (connections disappearing)
      mutation.removedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowRemoval(node);
        }
      });

      // Handle added nodes (connections appearing)
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowAddition(node);
        }
      });

      // Update row state after changes
      setTimeout(() => this.captureRowState(), 50);
    },

    handleStatusChange(element) {
      // Find the row containing this status element
      const row = element.closest('tr');
      if (!row) return;

      // Add a visual indicator that the status is changing
      row.classList.add('status-changing');

      // Add a subtle pulse animation to indicate the change
      gsap.fromTo(row,
        {
          backgroundColor: 'rgba(59, 130, 246, 0.15)',
          scale: 1
        },
        {
          duration: 0.6,
          backgroundColor: 'transparent',
          scale: 1.01,
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: () => {
            row.classList.remove('status-changing');
          }
        }
      );

      // The row removal will be handled by the main mutation observer
      // when LiveView updates the DOM
    },

    animateRowRemoval(row) {
      // Create a clone of the row for animation
      const clone = row.cloneNode(true);
      clone.style.position = 'absolute';
      clone.style.left = '0';
      clone.style.right = '0';
      clone.style.zIndex = '10';
      clone.style.pointerEvents = 'none';

      // Insert clone at the same position
      if (row.parentNode) {
        row.parentNode.insertBefore(clone, row.nextSibling);
      }

      // Animate the clone out
      gsap.to(clone, {
        duration: 0.5,
        x: -50,
        opacity: 0,
        scale: 0.95,
        ease: "power2.in",
        onComplete: () => {
          if (clone.parentNode) {
            clone.parentNode.removeChild(clone);
          }
        }
      });

      // Animate remaining rows moving up
      this.animateRemainingRows();
    },

    animateRowAddition(row) {
      // Skip if we're reordering (this might be a repositioned row)
      if (this.isReordering) return;

      // Set initial state for new row with more subtle entrance
      gsap.set(row, {
        y: -20,
        opacity: 0,
        scale: 0.95
      });

      // Animate row in with smooth entrance
      gsap.to(row, {
        duration: 0.5,
        y: 0,
        opacity: 1,
        scale: 1,
        ease: "power2.out",
        delay: 0.1
      });
    },

    animateRemainingRows() {
      if (!this.tableBody) return;

      const currentRows = this.tableBody.querySelectorAll('tr');

      currentRows.forEach((row, index) => {
        // Add subtle animation to show the table is updating
        gsap.fromTo(row,
          {
            y: -5,
            opacity: 0.8
          },
          {
            duration: 0.3,
            y: 0,
            opacity: 1,
            ease: "power2.out",
            delay: index * 0.02 // Stagger effect
          }
        );
      });
    },

    destroyed() {
      // Clean up observers
      if (this.observer) {
        this.observer.disconnect();
      }

      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      // Kill any running GSAP animations
      if (this.tableBody) {
        gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
      }
    }
  },

  // Hook for handling trace table animations with GSAP - inspired by infinite_scroll_packets.html
  TraceTableGSAP: {
    mounted() {
      this.setupGSAPAnimations();
      this.observeTableChanges();
      this.setupSmoothScrolling();
    },

    updated() {
      // Re-observe table changes when component updates
      this.observeTableChanges();
    },

    setupGSAPAnimations() {
      // Set up GSAP defaults for smooth, natural animations matching reference
      gsap.defaults({
        duration: 0.6,
        ease: "back.out(1.7)"
      });

      // Store reference to table body and wrapper for animations
      this.tableBody = this.el;
      this.tableWrapper = this.el.closest('.trace-message-table-wrapper');
      this.previousRows = new Map();

      if (this.tableBody) {
        // Store initial state of rows
        this.captureRowState();
      }
    },

    setupSmoothScrolling() {
      if (this.tableWrapper) {
        // Enable smooth scrolling with GSAP
        this.tableWrapper.style.scrollBehavior = 'auto'; // Disable CSS smooth scroll

        // Store scroll position for auto-scroll functionality
        this.isAtTop = true;
        this.userScrolled = false;

        // Listen for scroll events to detect user interaction
        this.tableWrapper.addEventListener('scroll', () => {
          const { scrollTop } = this.tableWrapper;
          this.isAtTop = scrollTop <= 10;
          this.userScrolled = true;

          // Reset user scroll flag after a delay
          clearTimeout(this.scrollTimeout);
          this.scrollTimeout = setTimeout(() => {
            this.userScrolled = false;
          }, 1000);
        });
      }
    },

    observeTableChanges() {
      if (this.observer) {
        this.observer.disconnect();
      }

      if (!this.tableBody) return;

      // Create mutation observer to watch for DOM changes
      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          this.handleRowChanges(mutation);
        });
      });

      // Start observing
      this.observer.observe(this.tableBody, {
        childList: true,
        subtree: false
      });
    },

    captureRowState() {
      if (!this.tableBody) return;

      const rows = this.tableBody.querySelectorAll('tr');
      this.previousRows.clear();

      rows.forEach((row) => {
        if (row.id) {
          this.previousRows.set(row.id, {
            element: row,
            rect: row.getBoundingClientRect()
          });
        }
      });
    },

    handleRowChanges(mutation) {
      // Handle added nodes (new messages) - inspired by infinite_scroll_packets.html
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          // Check if this is a new message (has trace-message-new class)
          const isNewMessage = node.classList.contains('trace-message-new');
          this.animateRowAddition(node, isNewMessage);
        }
      });

      // Handle removed nodes (messages being removed)
      mutation.removedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowRemoval(node);
        }
      });

      // Update row state after changes
      setTimeout(() => this.captureRowState(), 100);

      // Auto-scroll to top if user hasn't manually scrolled and we're viewing latest messages
      if (!this.userScrolled && this.isAtTop) {
        this.smoothScrollToTop();
      }
    },

    animateRowAddition(row, isNewMessage = true) {
      if (isNewMessage) {
        // Add CSS class for new row initial state
        row.classList.add('trace-row-new');

        // Set initial state for new row - matching infinite_scroll_packets.html style
        gsap.set(row, {
          opacity: 0,
          y: -30,
          scale: 0.95
        });

        // Add new message highlight effect
        row.classList.add('new-packet');

        // Set initial green highlight background
        gsap.set(row, {
          background: 'linear-gradient(90deg, #4caf50, transparent)'
        });

        // Animate row in with back.out easing like the reference
        gsap.to(row, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          ease: "back.out(1.7)",
          onComplete: () => {
            row.classList.remove('trace-row-new');
          }
        });

        // Animate the highlight background fade
        gsap.to(row, {
          background: 'transparent',
          duration: 2,
          ease: "power2.out",
          delay: 0.5,
          onComplete: () => {
            row.classList.remove('new-packet');
          }
        });

        // Push down other rows effect
        this.animateOtherRows(row);

        // Auto-scroll to top to show the new message
        setTimeout(() => {
          this.smoothScrollToTop();
        }, 100);
      } else {
        // For non-new messages (initial load), they are already visible by default
        // Just add a subtle fade-in effect
        gsap.fromTo(row,
          { opacity: 0 },
          {
            opacity: 1,
            duration: 0.5,
            ease: "power2.out",
            delay: Math.random() * 0.3
          }
        );
      }
    },

    animateOtherRows(newRow) {
      // Push down effect for other rows when new message arrives
      const otherRows = Array.from(this.tableBody.children).filter(row => row !== newRow);

      gsap.to(otherRows, {
        y: 5,
        duration: 0.3,
        stagger: 0.02,
        yoyo: true,
        repeat: 1,
        ease: "power2.out"
      });
    },

    animateRowRemoval(row) {
      // Simple fade out for removed rows
      gsap.to(row, {
        duration: 0.4,
        opacity: 0,
        x: -30,
        scale: 0.95,
        ease: "power2.in"
      });
    },

    smoothScrollToTop() {
      if (!this.tableWrapper) return;

      // Use GSAP for smooth scrolling to top (newest messages)
      gsap.to(this.tableWrapper, {
        duration: 0.8,
        scrollTo: { y: 0 },
        ease: "power2.out"
      });
    },

    destroyed() {
      // Clean up observers and timers
      if (this.observer) {
        this.observer.disconnect();
      }

      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }

      // Kill any running GSAP animations
      if (this.tableBody) {
        gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
      }

      if (this.tableWrapper) {
        gsap.killTweensOf(this.tableWrapper);
      }
    }
  },

  // Hook for handling trace row highlighting
  TraceRowHighlight: {
    mounted() {
      this.setupRowHighlighting();
    },

    updated() {
      this.setupRowHighlighting();
    },

    setupRowHighlighting() {
      const container = this.el;
      const tableBody = container.querySelector('#trace-message-table-body');
      if (!tableBody) return;

      // Remove existing event listeners to avoid duplicates
      if (this.handleRowClick) {
        tableBody.removeEventListener('click', this.handleRowClick);
      }

      // Add click event listener to the table body
      this.handleRowClick = (event) => {
        const clickedRow = event.target.closest('tr');
        if (!clickedRow || clickedRow.id === 'loading-more-row') return;

        // Remove highlight from all rows
        const allRows = tableBody.querySelectorAll('tr.trace-message-row');
        allRows.forEach(row => {
          row.classList.remove('js-highlighted');
        });

        // Add highlight to clicked row with animation
        clickedRow.classList.add('js-highlighted');

        // Add a subtle pulse effect
        clickedRow.style.animation = 'none';
        // Force reflow
        clickedRow.offsetHeight;
        clickedRow.style.animation = 'row-highlight-pulse 0.4s ease-out';

        // Remove the animation after it completes
        setTimeout(() => {
          if (clickedRow.style) {
            clickedRow.style.animation = '';
          }
        }, 400);
      };

      tableBody.addEventListener('click', this.handleRowClick);
      // Store reference for cleanup
      this.tableBody = tableBody;
    },

    destroyed() {
      if (this.tableBody && this.handleRowClick) {
        this.tableBody.removeEventListener('click', this.handleRowClick);
      }
    }
  },

  // Hook for JSON Viewer integration
  JsonViewer: {
    mounted() {
      this.initializeJsonViewer();
    },

    updated() {
      this.initializeJsonViewer();
    },

    initializeJsonViewer() {
      const container = this.el;
      const jsonData = container.dataset.json;

      if (!jsonData) {
        container.innerHTML = '<div class="json-viewer-error">No JSON data provided</div>';
        return;
      }

      try {
        // Parse the JSON data
        const parsedData = JSON.parse(jsonData);

        // Clear container
        container.innerHTML = '';

        // Create json-viewer element
        const viewer = document.createElement('json-viewer');
        viewer.data = parsedData;
        viewer.expanded = 2; // Expand 2 levels by default

        // Add custom styling classes
        viewer.classList.add('geeky-json-viewer');

        container.appendChild(viewer);
      } catch (error) {
        // If JSON parsing fails, show the raw data with error message
        container.innerHTML = `
          <div class="json-viewer-error">
            <div class="json-error-header">Invalid JSON Data</div>
            <pre class="json-raw-content">${jsonData}</pre>
          </div>
        `;
      }
    },

    destroyed() {
      // Clean up if needed
      if (this.el) {
        this.el.innerHTML = '';
      }
    }
  },

  // Hook for SlickGrid-based trace table with client-side filtering and pagination
  TraceSlickGrid: {
    mounted() {
      console.debug('TraceSlickGrid hook mounted');
      this.initializeSlickGrid();
      this.setupClientSideFiltering();
      this.setupStreamObserver();

      // Listen for grid data update events
      this.handleEvent("grid_data_update", (data) => {
        console.debug('Received grid_data_update event:', data);
        this.updateGridDataFromEvent(data);
      });

      // Listen for export events
      this.handleEvent("export_data", (data) => {
        console.debug('Received export_data event:', data);
        this.handleExportEvent(data);
      });

      console.debug('TraceSlickGrid hook initialized');
    },

    updated() {
      console.debug('TraceSlickGrid hook updated');
      // Update filters when component data attributes change
      this.updateFiltersFromDataAttributes();

      // Since we use phx-update="ignore", we need to manually check for data changes
      // The data attributes won't be automatically updated by LiveView
      console.debug('TraceSlickGrid updated hook called');
    },

    destroyed() {
      console.debug('TraceSlickGrid hook destroyed - cleaning up grid instance');

      // Clean up stream observer
      if (this.streamObserver) {
        this.streamObserver.disconnect();
        this.streamObserver = null;
      }

      // Clean up resize listener
      if (this.handleResize) {
        window.removeEventListener('resize', this.handleResize);
      }

      // Clean up SlickGrid instance
      if (this.grid) {
        try {
          // Unsubscribe from events to prevent memory leaks
          if (this.grid.onClick) {
            this.grid.onClick.unsubscribe();
          }

          // Destroy the grid instance
          this.grid.destroy();
          console.debug('SlickGrid instance destroyed');
        } catch (error) {
          console.warn('Error destroying SlickGrid instance:', error);
        }
        this.grid = null;
      }

      // Clean up DataView
      if (this.dataView) {
        try {
          // Unsubscribe from DataView events
          if (this.dataView.onRowCountChanged) {
            this.dataView.onRowCountChanged.unsubscribe();
          }
          if (this.dataView.onRowsChanged) {
            this.dataView.onRowsChanged.unsubscribe();
          }
          if (this.dataView.onGroupCollapsed) {
            this.dataView.onGroupCollapsed.unsubscribe();
          }
          if (this.dataView.onGroupExpanded) {
            this.dataView.onGroupExpanded.unsubscribe();
          }
        } catch (error) {
          console.warn('Error cleaning up DataView events:', error);
        }
        this.dataView = null;
      }

      // Clean up other references
      this.groupItemMetadataProvider = null;
      this.columns = null;
      this.options = null;
      this.filters = null;
      this.groupExpandStates = null;

      console.debug('TraceSlickGrid cleanup completed');
    },
  
    initializeSlickGrid() {
      const container = this.el;
      const brokerId = container.dataset.brokerName || 'default';

      console.debug('Initializing SlickGrid on container:', container);
      console.debug('Container ID:', container.id);
      console.debug('Broker ID:', brokerId);
      console.debug('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight);

      // Check if grid already exists to prevent double initialization
      if (this.grid) {
        console.warn('SlickGrid already initialized for broker:', brokerId, '- skipping initialization');
        return;
      }

      // Define columns for the trace grid with responsive width constraints
      this.columns = this.getResponsiveColumns();

      // Grid options - configured for read-only display like DaisyUI table
      this.options = {
        enableCellNavigation: true,
        enableColumnReorder: false,
        multiColumnSort: false,
        rowHeight: 28,
        headerRowHeight: 40,
        enableAsyncPostRender: false,
        forceFitColumns: this.shouldForceFitColumns(), // Dynamic based on screen size
        enableTextSelectionOnCells: false,
        viewportClass: 'trace-grid-viewport',
        editable: false,
        autoEdit: false,
        enableAddRow: false,
        enableCellRangeSelection: false,
        enableRowReordering: false,
        // Enable responsive behavior
        autoWidth: false, // Let columns use their defined widths
        syncColumnCellResize: true
      };

      // Initialize group item metadata provider for grouping functionality
      console.debug('Initializing SlickGroupItemMetadataProvider...');
      this.groupItemMetadataProvider = new SlickGroupItemMetadataProvider();
      console.debug('SlickGroupItemMetadataProvider initialized:', this.groupItemMetadataProvider);

      // Initialize data view for virtual scrolling and filtering
      this.dataView = new SlickDataView({
        groupItemMetadataProvider: this.groupItemMetadataProvider,
        inlineFilters: true
      });

      // Initialize filter state
      this.filters = {
        topic_filter: '',
        payload_filter: '',
        selected_client_ids: [],
        ignore_ping_packets: true,
        topic_grouping_enabled: false
      };

      // Initialize grouping state from data attributes
      const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
      this.groupingEnabled = topicGroupingEnabled;

      // Initialize group expand/collapse state storage
      this.groupExpandStates = new Map();

      // Initialize pending restoration flag
      this.pendingGroupStateRestoration = false;

      console.debug('Initial grouping state from data attributes:', topicGroupingEnabled);

      // Initialize the grid
      this.grid = new SlickGrid(container, this.dataView, this.columns, this.options);

      // Register the group item metadata provider to add expand/collapse group handlers
      this.grid.registerPlugin(this.groupItemMetadataProvider);

      // TODO: Re-enable failed ACK message styling after fixing group compatibility
      // For now, we'll disable this to ensure group functionality works correctly
      // this.dataView.getItemMetadata = this.getRowMetadata.bind(this);

      console.debug('SlickGrid initialized:', this.grid);
      console.debug('Grid viewport:', this.grid.getViewport());

      // Set up event handlers
      this.grid.onClick.subscribe((e, args) => {
        // Handle row clicks for message selection
        const item = this.dataView.getItem(args.row);
        if (item && !item.__group) {
          this.pushEventTo(this.el, 'select_message', { id: item.id });
        }
      });

      // Set up data view events
      this.dataView.onGroupCollapsed.subscribe((e, args) => {
        console.log('Group collapsed:', args.groupingKey);
        this.captureGroupStatesOnChange(args.groupingKey, false);        
        this.grid.render();

      });
      this.dataView.onGroupExpanded.subscribe((e, args) => {
        console.log('Group expanded:', args.groupingKey);
        this.captureGroupStatesOnChange(args.groupingKey, true);
        this.grid.render();

      });
      this.dataView.onRowsChanged.subscribe((e, args) => {
        // SlickGrid automatically handles rendering for onRowsChanged
        // Only invalidate specific rows if provided
        if (args.rows && args.rows.length > 0) {
          this.grid.invalidateRows(args.rows);
        } else {
          this.grid.invalidate();
        }

        this.updateMessageCount();

        // Handle pending group state restoration after grouping operations
        this.handlePendingGroupStateRestoration();
      });

      // Load initial data
      this.updateGridData();

      // Set up window resize listener for responsive behavior
      this.handleResize = this.debounce(() => {
        this.handleWindowResize();
      }, 250);

      window.addEventListener('resize', this.handleResize);

      // Force resize after initialization
      setTimeout(() => {
        this.grid.resizeCanvas();
        this.grid.render();
        console.debug('Grid resized and re-rendered');
        console.debug('Grid container HTML:', this.el.innerHTML.slice(0, 500));
        console.debug('Grid canvas element:', this.el.querySelector('.slick-viewport'));

        // Check computed styles
        const headerColumns = this.el.querySelector('.slick-header-columns');
        if (headerColumns) {
          const computedStyle = window.getComputedStyle(headerColumns);
          console.debug('Header columns computed style:', {
            left: computedStyle.left,
            width: computedStyle.width,
            position: computedStyle.position,
            display: computedStyle.display
          });
        }

        // Force autosizeColumns if available
        if (this.grid.autosizeColumns) {
          this.grid.autosizeColumns();
          console.debug('Autosized columns');
        }

        // Fix column positions
        this.fixColumnPositions();
      }, 100);
    },

    setupClientSideFiltering() {
      // Set up client-side filtering using SlickGrid's DataView
      try {
        console.debug('Setting up client-side filtering...');
        console.debug('DataView available:', !!this.dataView);
        console.debug('FilterFunction available:', !!this.filterFunction);

        // Initialize filters first before setting up filtering
        this.updateFiltersFromDataAttributes();

        // Now set up the filter function
        if (this.dataView) {
          // Create a filter function that has access to the hook's filters
          this.createAndSetFilterFunction();
          console.debug('Filter function set successfully');
        } else {
          console.warn('DataView not available for filtering setup');
        }
      } catch (error) {
        console.error('Error setting up client-side filtering:', error);
      }
    },

    // Create and set a filter function that works with SlickGrid's compilation
    createAndSetFilterFunction() {
      // Store a reference to the hook's filters in the DataView for access
      this.dataView._hookFilters = this.filters;
      this.dataView._hookInstance = this;

      // Create a filter function that can access the stored filters
      const filterFunction = function(item) {
        const filters = this._hookFilters;
        const hook = this._hookInstance;

        if (!filters) return true;

        // PING packet filter
        if (filters.ignore_ping_packets &&
            (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
          return false;
        }

        // Topic filter
        if (filters.topic_filter && filters.topic_filter.trim() !== '') {
          const topic = item.topic || '';
          if (!hook.matchesTopic(topic, filters.topic_filter)) {
            return false;
          }
        }

        // Payload filter
        if (filters.payload_filter && filters.payload_filter.trim() !== '') {
          const payload = item.payload || '';
          if (!payload.toLowerCase().includes(filters.payload_filter.toLowerCase())) {
            return false;
          }
        }

        // Client ID filter
        if (filters.selected_client_ids.length > 0) {
          if (!filters.selected_client_ids.includes(item.client_id)) {
            return false;
          }
        }

        return true;
      };

      this.dataView.setFilter(filterFunction);
    },

    // Update the filter function when filters change
    updateFilterFunction() {
      if (this.dataView) {
        this.dataView._hookFilters = this.filters;
        this.dataView.refresh();
      }
    },

    filterFunction(item) {
      // Apply all filters to determine if item should be visible

      // PING packet filter
      if (this.filters.ignore_ping_packets &&
          (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
        return false;
      }

      // Topic filter
      if (this.filters.topic_filter && this.filters.topic_filter.trim() !== '') {
        const topic = item.topic || '';
        if (!this.matchesTopic(topic, this.filters.topic_filter)) {
          return false;
        }
      }

      // Payload filter
      if (this.filters.payload_filter && this.filters.payload_filter.trim() !== '') {
        const payload = item.payload || '';
        if (!payload.toLowerCase().includes(this.filters.payload_filter.toLowerCase())) {
          return false;
        }
      }

      // Client ID filter
      if (this.filters.selected_client_ids.length > 0) {
        if (!this.filters.selected_client_ids.includes(item.client_id)) {
          return false;
        }
      }

      return true;
    },

    matchesTopic(topic, pattern) {
      // Simple MQTT topic matching - can be enhanced for wildcards
      if (pattern.includes('+') || pattern.includes('#')) {
        // Convert MQTT pattern to regex
        const regexPattern = pattern
          .replace(/\+/g, '[^/]+')
          .replace(/#/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(topic);
      } else {
        return topic.includes(pattern);
      }
    },

    updateFiltersFromDataAttributes() {
      // Read filter state from data attributes
      try {
        this.filters.topic_filter = this.el.dataset.topicFilter || '';
        this.filters.payload_filter = this.el.dataset.payloadFilter || '';
        this.filters.ignore_ping_packets = this.el.dataset.ignorePingPackets === 'true';
        this.filters.topic_grouping_enabled = this.el.dataset.topicGroupingEnabled === 'true';

        const selectedClientIds = this.el.dataset.selectedClientIds;
        if (selectedClientIds) {
          this.filters.selected_client_ids = JSON.parse(selectedClientIds);
        } else {
          this.filters.selected_client_ids = [];
        }

        // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
        const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
        this.groupingEnabled = topicGroupingEnabled;
        this.filters.topic_grouping_enabled = topicGroupingEnabled;

        console.debug('Synced grouping state from data attributes:', topicGroupingEnabled);

        // Update grouping state - this will also ensure filters are properly applied
        this.updateGrouping(topicGroupingEnabled);

      } catch (error) {
        console.error('Error reading filter data attributes:', error);
      }
    },

    updateFiltersFromEvent(data) {
      console.debug('updateFiltersFromEvent called with data:', data);

      // Update filters from LiveView event
      this.filters.topic_filter = data.topic_filter || '';
      this.filters.payload_filter = data.payload_filter || '';
      this.filters.selected_client_ids = data.selected_client_ids || [];
      this.filters.ignore_ping_packets = data.ignore_ping_packets;
      this.filters.topic_grouping_enabled = data.topic_grouping_enabled || false;

      // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
      const topicGroupingEnabled = data.topic_grouping_enabled || false;
      this.groupingEnabled = topicGroupingEnabled;
      this.filters.topic_grouping_enabled = topicGroupingEnabled;

      console.debug('Synced grouping state from event:', topicGroupingEnabled);

      // Update grouping state - this will also ensure filters are properly applied
      this.updateGrouping(topicGroupingEnabled);
    },

    updateGridData() {
      try {
        const gridDataJson = this.el.dataset.gridData;
        if (gridDataJson) {
          const gridData = JSON.parse(gridDataJson);

          // Validate that gridData is an array
          if (!Array.isArray(gridData)) {
            console.warn('Grid data is not an array:', gridData);
            return;
          }

          // Ensure all items have valid IDs, generate if missing
          const validGridData = gridData.map(item => {
            if (!item || typeof item !== 'object') {
              console.warn('Filtering out invalid item:', item);
              return null;
            }

            // Generate ID if missing or invalid
            if (!item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
              console.warn('Generating ID for item without valid id:', item);
              item.id = `generated_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            }

            // Ensure ID is a string for consistency
            item.id = String(item.id);

            return item;
          }).filter(item => item !== null);

          if (validGridData.length !== gridData.length) {
            console.warn(`Filtered out ${gridData.length - validGridData.length} invalid items`);
          }

          this.dataView.beginUpdate();
          // Use ID field for deduplication in all cases
          // SlickGrid requires unique IDs even when grouping is enabled
          this.dataView.setItems(validGridData, 'id');
          this.dataView.endUpdate();

          // Apply filters after loading data
          this.dataView.refresh();

          // Re-apply grouping if enabled, preserving existing group states
          if (this.groupingEnabled) {
            // Re-apply grouping configuration
            this.dataView.setGrouping([{
              getter: (item) => {
                // Extract topic for grouping - handle both direct topic field and topics array
                if (typeof item.topic === 'string' && item.topic) {
                  return item.topic;
                } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
                  const firstTopic = item.topics[0];
                  if (typeof firstTopic === 'object' && firstTopic.topic) {
                    return firstTopic.topic;
                  } else if (typeof firstTopic === 'string') {
                    return firstTopic;
                  }
                }
                return 'No Topic';
              },
              formatter: (g) => {
                // Format the group header with topic name and count
                const topicName = g.value || 'No Topic';
                return `<span class="font-semibold">Topic: ${topicName}</span> <span class="badge badge-sm badge-outline ml-2">${g.count} messages</span>`;
              },
              aggregateCollapsed: false, // Calculate aggregates for collapsed groups
              collapsed: true, // Start with groups collapsed by default
              lazyTotalsCalculation: true
            }]);

            // Group state restoration will be handled by the event-driven mechanism
          }

          // Force grid to render
          this.grid.invalidate();
          this.grid.render();

          // Fix column positions after render
          this.fixColumnPositions();

          // Message count will be updated by onRowsChanged event

          // Log for debugging
          console.debug(`Updated grid with ${gridData.length} items`);
          console.debug('Grid data length:', this.dataView.getLength());
          console.debug('Grid viewport after update:', this.grid.getViewport());
        }
      } catch (error) {
        console.error('Error updating grid data:', error);
        console.error('Grid data JSON:', this.el.dataset.gridData);
      }
    },

    updateGridDataFromEvent(data) {
      try {
        const gridData = data.grid_data;
        const updateType = data.update_type || 'replace';

        // Validate that gridData is an array
        if (!Array.isArray(gridData)) {
          console.warn('Grid data from event is not an array:', gridData);
          return;
        }

        // Validate that all items have an 'id' property and filter out any that don't
        const validGridData = gridData.map(item => {
          if (!item || typeof item !== 'object' || !item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
            console.warn('Filtering out event item without valid id:', item);
            return null;
          }
          // Ensure ID is a string for consistency
          item.id = String(item.id);
          return item;
        }).filter(item => item !== null);

        if (validGridData.length !== gridData.length) {
          console.warn(`Filtered out ${gridData.length - validGridData.length} event items without valid IDs`);
        }

        if (validGridData.length === 0 && gridData.length > 0) {
          console.debug('No valid items to process from event after filtering');
          return;
        }

        this.dataView.beginUpdate();

        if (updateType === 'add') {
          // Incremental update - add new items to the beginning
          console.debug(`Adding ${validGridData.length} new items to grid`);

          // Add items to the beginning (newest first)
          for (let i = validGridData.length - 1; i >= 0; i--) {
            this.dataView.insertItem(0, validGridData[i]);
          }
        } else {
          // Full replacement
          console.debug(`Replacing grid data with ${validGridData.length} items`);
          this.dataView.setItems(validGridData, 'id');
        }

        this.dataView.endUpdate();

        // Handle grouping based on update type
        if (this.groupingEnabled) {
          if (updateType === 'add') {
            // For incremental updates, use optimized approach
            this.handleIncrementalGroupingUpdate();
          } else {
            // For full replacement, re-apply grouping configuration
            this.applyGroupingConfiguration();
          }
        }

        // Fix column positions after data update
        this.fixColumnPositions();

        // Message count will be updated by onRowsChanged event

        // Log for debugging
        console.debug(`Updated grid from event with ${gridData.length} items (${updateType})`);
        console.debug('Grid data length:', this.dataView.getLength());
        console.debug('Grid viewport after update:', this.grid.getViewport());
      } catch (error) {
        console.error('Error updating grid data from event:', error);
        console.error('Event data:', data);
      }
    },

    fixColumnPositions() {
      console.debug('fixColumnPositions called');

      // Let SlickGrid handle its own positioning
      // Force a resize to ensure proper column alignment
      if (this.grid && this.grid.resizeCanvas) {
        this.grid.resizeCanvas();
        console.debug('Grid canvas resized');
      }

      // Force column autosizing if available
      if (this.grid && this.grid.autosizeColumns) {
        this.grid.autosizeColumns();
        console.debug('Columns autosized');
      }
    },

    updateMessageCount() {
      // Update the message count display in the UI
      const messageCountElement = document.getElementById('message-count-display');
      if (messageCountElement && this.dataView) {        
        const totalCount = this.dataView.getItems().length;        
        messageCountElement.textContent = `${totalCount} messages`;
      }
    },

    updateGrouping(enabled) {
      if (!this.dataView) return;

      this.groupingEnabled = enabled;

      try {
        // Always ensure the filter function is active BEFORE setting grouping
        this.updateFilterFunction();

        if (enabled) {
          // Enable grouping using the extracted method
          this.applyGroupingConfiguration();          
        } else {
          // Disable grouping - pass empty array to clear all grouping
          this.dataView.setGrouping([]);
          // Clear stored group states when disabling grouping
          this.groupExpandStates.clear();
          // Manual refresh since we've cleared grouping
          this.dataView.refresh();
        }
        this.grid.render();

        // Grid will auto-render after dataView operations
      } catch (error) {
        console.error('Error updating grouping:', error);
      }
    },

    // Optimized grid refresh method with performance hints
    refreshGridWithOptimization(isFilterNarrowing = false, isFilterExpanding = false) {
      try {
        const renderedRange = this.grid.getRenderedRange();

        // Set refresh hints for better performance
        this.dataView.setRefreshHints({
          ignoreDiffsBefore: renderedRange.top,
          ignoreDiffsAfter: renderedRange.bottom + 1,
          isFilterNarrowing: isFilterNarrowing,
          isFilterExpanding: isFilterExpanding
        });

        // Refresh the grid to apply changes
        this.dataView.refresh();
        // Grid will auto-render after dataView.refresh()
      } catch (error) {
        console.error('Error refreshing grid:', error);
        // Fallback to simple refresh
        this.dataView.refresh();
      }
    },

    // Enhanced filter update method that works with grouping
    updateFiltersAndRefresh(filterChanges = {}) {
      console.debug('Updating filters with changes:', filterChanges);

      // Track previous filter state for optimization hints
      const prevFilters = { ...this.filters };

      // Apply filter changes
      Object.assign(this.filters, filterChanges);

      // Determine if filter is narrowing or expanding for optimization
      const isFilterNarrowing = this.isFilterNarrowing(prevFilters, this.filters);
      const isFilterExpanding = this.isFilterExpanding(prevFilters, this.filters);

      // Always ensure the filter function is active (works with both grouped and ungrouped data)
      this.updateFilterFunction();

      // Use optimized refresh
      this.refreshGridWithOptimization(isFilterNarrowing, isFilterExpanding);

      console.debug('Filters updated successfully');
    },

    // Helper method to determine if filter is becoming more restrictive
    isFilterNarrowing(prevFilters, newFilters) {
      // Topic filter is narrowing if it's longer or more specific
      if (newFilters.topic_filter.length > prevFilters.topic_filter.length) {
        return true;
      }

      // Payload filter is narrowing if it's longer
      if (newFilters.payload_filter.length > prevFilters.payload_filter.length) {
        return true;
      }

      // Client ID filter is narrowing if more clients are selected
      if (newFilters.selected_client_ids.length > prevFilters.selected_client_ids.length) {
        return true;
      }

      // PING filter is narrowing if it's now enabled
      if (!prevFilters.ignore_ping_packets && newFilters.ignore_ping_packets) {
        return true;
      }

      return false;
    },

    // Helper method to determine if filter is becoming less restrictive
    isFilterExpanding(prevFilters, newFilters) {
      // Topic filter is expanding if it's shorter or removed
      if (newFilters.topic_filter.length < prevFilters.topic_filter.length) {
        return true;
      }

      // Payload filter is expanding if it's shorter or removed
      if (newFilters.payload_filter.length < prevFilters.payload_filter.length) {
        return true;
      }

      // Client ID filter is expanding if fewer clients are selected
      if (newFilters.selected_client_ids.length < prevFilters.selected_client_ids.length) {
        return true;
      }

      // PING filter is expanding if it's now disabled
      if (prevFilters.ignore_ping_packets && !newFilters.ignore_ping_packets) {
        return true;
      }

      return false;
    },

    // Handle pending group state restoration after grouping operations
    handlePendingGroupStateRestoration() {
      if (!this.pendingGroupStateRestoration || !this.groupingEnabled) return;

      try {
        // Reset the flag first to prevent multiple calls
        this.pendingGroupStateRestoration = false;

        // Restore group states now that grouping is complete
        this.restoreGroupStates();

        console.debug('✅ Group state restoration completed via onRowsChanged event');
      } catch (error) {
        console.warn('Error in pending group state restoration:', error);
      }
    },

    // Schedule group state restoration for next grouping operation
    scheduleGroupStateRestoration() {
      this.pendingGroupStateRestoration = true;
      console.debug('📅 Group state restoration scheduled');
    },

    // Apply grouping configuration (extracted for reuse)
    applyGroupingConfiguration(shouldScheduleRestore = true) {
      if (!this.dataView) return;

      // Schedule state restoration if requested
      if (shouldScheduleRestore && this.groupExpandStates.size > 0) {
        this.scheduleGroupStateRestoration();
      }

      this.dataView.setGrouping([{
          getter: (item) => {
            // Extract topic for grouping - handle both direct topic field and topics array
            if (typeof item.topic === 'string' && item.topic) {
              return item.topic;
            } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
              const firstTopic = item.topics[0];
              if (typeof firstTopic === 'object' && firstTopic.topic) {
                return firstTopic.topic;
              } else if (typeof firstTopic === 'string') {
                return firstTopic;
              }
            }
            return 'No Topic';
          },
          formatter: (g) => {
            // Format the group header with topic name and count
            const topicName = g.value || 'No Topic';
            return `<span class="font-semibold">Topic: ${topicName}</span> <span class="badge badge-sm badge-outline ml-2">${g.count} messages</span>`;
          },
          aggregateCollapsed: false, // Calculate aggregates for collapsed groups
          collapsed: true, // Start with groups collapsed by default
          lazyTotalsCalculation: true
        }]);

      // Apply filters after setting grouping
      this.dataView.refresh();
    },

    // Handle incremental grouping updates without triggering collapse events
    handleIncrementalGroupingUpdate() {
      if (!this.dataView) return;

      try {
        // Use optimized refresh with hints for incremental updates
        const renderedRange = this.grid.getRenderedRange();
        this.dataView.setRefreshHints({
          ignoreDiffsBefore: renderedRange.top,
          ignoreDiffsAfter: renderedRange.bottom + 1,
          isFilterNarrowing: false,
          isFilterExpanding: false
        });

        // Just refresh to update group counts - don't re-apply grouping
        this.dataView.refresh();

        console.debug('✅ Incremental grouping update completed without re-grouping');
      } catch (error) {
        console.warn('Error in incremental grouping update:', error);
        // Fallback to simple refresh
        this.dataView.refresh();
      }
    },

    // Capture group states when they change - optimized to update only specific key
    captureGroupStatesOnChange(groupingKey, isExpanded) {
      if (!this.dataView || !this.groupingEnabled) return;

      try {
        // If specific grouping key and state are provided, update only that key
        if (groupingKey !== undefined && isExpanded !== undefined) {
          const previousState = this.groupExpandStates.get(groupingKey);

          // Only update if state actually changed
          if (previousState !== isExpanded) {
            this.groupExpandStates.set(groupingKey, isExpanded);
            console.debug(`🔄 Group "${groupingKey}" state changed: ${previousState ? 'expanded' : 'collapsed'} → ${isExpanded ? 'expanded' : 'collapsed'}`);
          }
          return;
        }

      } catch (error) {
        console.warn('Error capturing group states on change:', error);
      }
    },

    // Restore group expand/collapse states
    restoreGroupStates() {
      if (!this.dataView || !this.groupingEnabled || this.groupExpandStates.size === 0) return;

      try {
        let restoredCount = 0;
        this.groupExpandStates.forEach((isExpanded, groupValue) => {
          // Check current state to avoid unnecessary operations
          const groups = this.dataView.getGroups();
          const currentGroup = groups.find(g => g.value === groupValue);

          if (currentGroup) {
            const currentlyExpanded = !currentGroup.collapsed;

            // Only change state if it's different from desired state
            if (currentlyExpanded !== isExpanded) {
              if (isExpanded) {
                this.dataView.expandGroup(groupValue);
              } else {
                this.dataView.collapseGroup(groupValue);
              }
              console.debug(`🔄 Restored group "${groupValue}" to ${isExpanded ? 'expanded' : 'collapsed'} state`);
              restoredCount++;
            }
          }
        });

        if (restoredCount > 0) {
          console.debug(`✅ Restored ${restoredCount} group states (${this.groupExpandStates.size} total stored)`);
        }
      } catch (error) {
        console.warn('Error restoring group states:', error);
      }
    },

    setupStreamObserver() {
      // Find the hidden stream container
      const streamContainer = document.getElementById('trace-messages-stream');
      if (!streamContainer) {
        console.debug('Stream container not found, skipping stream observer setup');
        return;
      }

      console.debug('Setting up stream observer for trace messages');

      // Create a MutationObserver to watch for new stream items
      this.streamObserver = new MutationObserver((mutations) => {
        let newMessages = [];
        let seenMessageIds = new Set(); // Track message IDs within this batch to avoid duplicates

        console.debug(`MutationObserver triggered with ${mutations.length} mutations`);

        mutations.forEach((mutation, mutationIndex) => {
          console.debug(`Mutation ${mutationIndex}: type=${mutation.type}, addedNodes=${mutation.addedNodes.length}, removedNodes=${mutation.removedNodes.length}`);

          if (mutation.type === 'childList') {
            // Check for added nodes (new stream items)
            mutation.addedNodes.forEach((node, nodeIndex) => {
              console.debug(`  AddedNode ${nodeIndex}: nodeType=${node.nodeType}, tagName=${node.tagName}, hasDataMessage=${!!node.dataset?.message}`);

              if (node.nodeType === Node.ELEMENT_NODE && node.dataset.message) {
                try {
                  const message = JSON.parse(node.dataset.message);
                  console.debug(`  Parsed message: id=${message.id}, type=${message.type}`);

                  // Check if we've already seen this message ID in this batch
                  if (!seenMessageIds.has(message.id)) {
                    seenMessageIds.add(message.id);
                    newMessages.push(message);
                    console.debug(`  Added message ${message.id} to batch`);
                  } else {
                    console.debug(`  Skipped duplicate message ${message.id} in same batch`);
                  }
                } catch (error) {
                  console.error('Error parsing stream message:', error);
                }
              }
            });
          }
        });

        // If we have new messages, add them to the grid
        if (newMessages.length > 0) {
          console.debug(`Stream observer detected ${newMessages.length} unique new messages (after batch deduplication)`);
          this.addNewMessagesToGrid(newMessages);
        }
      });

      // Start observing the stream container
      this.streamObserver.observe(streamContainer, {
        childList: true,
        subtree: false
      });

      console.debug('Stream observer setup complete');
    },

    addNewMessagesToGrid(newMessages) {
      if (!this.dataView || !Array.isArray(newMessages) || newMessages.length === 0) {
        return;
      }

      try {
        console.debug(`Adding ${newMessages.length} new messages to grid`);

        // Validate that all new messages have an 'id' property and filter out any that don't
        const validNewMessages = newMessages.map(item => {
          if (!item || typeof item !== 'object' || !item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
            console.warn('Filtering out new message without valid id:', item);
            return null;
          }
          // Ensure ID is a string for consistency
          item.id = String(item.id);
          return item;
        }).filter(item => item !== null);

        if (validNewMessages.length !== newMessages.length) {
          console.warn(`Filtered out ${newMessages.length - validNewMessages.length} new messages without valid IDs`);
        }

        if (validNewMessages.length === 0) {
          console.debug('No valid new messages to add after filtering');
          return;
        }

        // Get current data from DataView
        const currentData = this.dataView.getItems();

        // Ensure all current data items have valid IDs, generate if missing
        const validCurrentData = currentData.map(item => {
          if (!item || typeof item !== 'object') {
            console.warn('Filtering out invalid existing item:', item);
            return null;
          }

          // Generate ID if missing or invalid
          if (!item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
            console.warn('Generating ID for existing item without valid id:', item);
            item.id = `generated_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          }

          // Ensure ID is a string for consistency
          item.id = String(item.id);

          return item;
        }).filter(item => item !== null);

        if (validCurrentData.length !== currentData.length) {
          console.warn(`Filtered out ${currentData.length - validCurrentData.length} invalid existing items`);
        }

        // Merge new messages with existing data, newest first        
        const mergedData = [...validNewMessages, ...validCurrentData];

        this.dataView.beginUpdate();

        // Use ID field for deduplication in all cases
        // SlickGrid requires unique IDs even when grouping is enabled
        this.dataView.setItems(mergedData, 'id');

        this.dataView.endUpdate();

        console.debug(`Successfully processed ${newMessages.length} messages (DataView automatically handled duplicates)`);

        // Handle grouping for incremental updates
        if (this.groupingEnabled) {
          this.handleIncrementalGroupingUpdate();
        }

        // Fix column positions after data update
        this.fixColumnPositions();

      } catch (error) {
        console.error('Error adding new messages to grid:', error);
      }
    },

    // Window resize handler for responsive behavior
    handleWindowResize() {
      if (!this.grid) return;

      console.debug('Window resized, updating grid responsiveness');

      // Update columns for new screen size
      const newColumns = this.getResponsiveColumns();
      this.grid.setColumns(newColumns);

      // Update grid options for new screen size
      const shouldForceFit = this.shouldForceFitColumns();
      this.grid.setOptions({ forceFitColumns: shouldForceFit });

      // Resize canvas and re-render
      this.grid.resizeCanvas();
      this.grid.render();

      console.debug('Grid updated for new screen size');
    },

    // Utility method for debouncing
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Responsive helper methods
    shouldForceFitColumns() {
      // Always enable force fit to make table use full available width
      // This ensures the table adapts to both small and large screens
      return true;
    },

    getResponsiveColumns() {
      const screenWidth = window.innerWidth;
      const isMobile = screenWidth <= 480;
      const isTablet = screenWidth <= 768;

      // Base column definitions - use relative widths for forceFitColumns
      const baseColumns = [
        {
          id: "packet_id",
          name: "Packet ID",
          field: "packet_id",
          // Use minWidth only to ensure readability, let forceFitColumns handle the rest
          minWidth: isMobile ? 60 : isTablet ? 70 : 80,
          formatter: this.packetIdFormatter
        },
        {
          id: "timestamp",
          name: "Timestamp",
          field: "timestamp",
          minWidth: isMobile ? 100 : isTablet ? 120 : 140,
          formatter: this.timestampFormatter
        },
        {
          id: "client_id",
          name: "Client ID",
          field: "client_id",
          minWidth: isMobile ? 80 : isTablet ? 90 : 100,
          formatter: this.clientIdFormatter
        },
        {
          id: "direction",
          name: "Direction",
          field: "direction",
          minWidth: isMobile ? 60 : isTablet ? 70 : 80,
          formatter: this.directionFormatter
        },
        {
          id: "type",
          name: "Type",
          field: "type",
          minWidth: isMobile ? 70 : isTablet ? 80 : 90,
          formatter: this.typeFormatter
        },
        {
          id: "topic",
          name: "Topic",
          field: "topic",
          minWidth: isMobile ? 100 : isTablet ? 110 : 120,
          formatter: this.topicFormatter
        },
        {
          id: "payload",
          name: "Payload ｜ Reason",
          field: "payload",
          minWidth: isMobile ? 120 : isTablet ? 150 : 200,
          formatter: this.payloadFormatter
        },
        {
          id: "data_size",
          name: "Payload/Total",
          field: "data_size",
          minWidth: isMobile ? 70 : isTablet ? 80 : 90,
          formatter: this.dataSizeFormatter
        },
        {
          id: "qos",
          name: "QoS",
          field: "qos",
          minWidth: isMobile ? 30 : isTablet ? 40 : 50
        },
        {
          id: "retain",
          name: "Retain",
          field: "retain",
          minWidth: isMobile ? 40 : isTablet ? 50 : 60,
          formatter: this.retainFormatter
        }
      ];

      // On very small screens, hide some less critical columns
      if (isMobile) {
        return baseColumns.filter(col =>
          !['data_size', 'qos', 'retain'].includes(col.id)
        );
      }

      return baseColumns;
    },

    // Formatters for different columns
    packetIdFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      return value === "undefined" ? 'N/A' : (value || 'N/A');
    },

    timestampFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      if (!value) return 'N/A';
      try {
        // Value is now integer microseconds since Unix epoch
        const date = new Date(value / 1000); // Convert microseconds to milliseconds

        // Get time zone from the grid element's data attribute
        const gridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
        const timeZone = gridElement ? gridElement.dataset.timeZone : 'UTC';

        // Convert to timezone to match backend formatting
        const options = {
          timeZone: timeZone,
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        };

        const formatter = new Intl.DateTimeFormat('en-CA', options);
        const parts = formatter.formatToParts(date);

        const month = parts.find(part => part.type === 'month').value;
        const day = parts.find(part => part.type === 'day').value;
        const hour = parts.find(part => part.type === 'hour').value;
        const minute = parts.find(part => part.type === 'minute').value;
        const second = parts.find(part => part.type === 'second').value;

        // Get milliseconds from the original microsecond value
        const ms = String(Math.floor((value % 1000000) / 1000)).padStart(3, '0');

        return `${month}-${day} ${hour}:${minute}:${second}.${ms}`;
      } catch (error) {
        console.error('Error formatting timestamp:', error, 'value:', value);
        return 'Invalid';
      }
    },

    clientIdFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      return `<span class="badge badge-ghost badge-sm">${value || 'unknown'}</span>`;
    },

    directionFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      let badgeClass, icon;
      if (value === 'IN') {
        badgeClass = 'badge-success';
        icon = '«';
      } else if (value === 'OUT') {
        badgeClass = 'badge-warning';
        icon = '»';
      } else if (value === 'SYSTEM') {
        badgeClass = 'badge-error';
        icon = '⚠';
      } else {
        badgeClass = 'badge-ghost';
        icon = '⚬';
      }
      return `<span class="badge badge-sm ${badgeClass}">${icon} ${value || 'N/A'}</span>`;
    },

    typeFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      // Inline the getTypeBadgeClass logic to avoid context issues
      let badgeClass;
      switch (value) {
        case 'PUBLISH':
        case 'PUBACK':
        case 'PUBREC':
        case 'PUBREL':
        case 'PUBCOMP':
          badgeClass = 'badge-info';
          break;
        case 'SUBSCRIBE':
        case 'SUBACK':
        case 'UNSUBSCRIBE':
        case 'UNSUBACK':
          badgeClass = 'badge-accent';
          break;
        case 'CONNECT':
        case 'CONNACK':
          badgeClass = 'badge-success';
          break;
        case 'DISCONNECT':
          badgeClass = 'badge-warning';
          break;
        case 'CONNECTION_ERROR':
          badgeClass = 'badge-error';
          break;
        case 'PINGREQ':
        case 'PINGRESP':
          badgeClass = 'badge-secondary';
          break;
        case 'AUTH':
          badgeClass = 'badge-primary';
          break;
        default:
          badgeClass = 'badge-ghost';
      }
      return `<span class="badge badge-sm badge-outline ${badgeClass}">${value}</span>`;
    },

    topicFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      // Handle both old string format and new serialized format
      let topicText = 'N/A';

      if (typeof value === 'string' && value) {
        topicText = value;
      } else if (dataContext && dataContext.topics && Array.isArray(dataContext.topics) && dataContext.topics.length > 0) {
        // Use the first topic from the topics array
        const firstTopic = dataContext.topics[0];
        if (typeof firstTopic === 'object' && firstTopic.topic) {
          topicText = firstTopic.topic;
        } else if (typeof firstTopic === 'string') {
          topicText = firstTopic;
        }
      }

      return `<span title="${topicText}">${topicText}</span>`;
    },

    payloadFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      if (!value) return '';

      // For ACK messages with reason code information, show full text without truncation
      const ackTypes = ['PUBACK', 'PUBREC', 'PUBREL', 'PUBCOMP', 'SUBACK', 'UNSUBACK', 'CONNACK', 'DISCONNECT'];
      if (dataContext && ackTypes.includes(dataContext.type) && value.includes('Success') || value.includes('Error') || value.includes('Refused') || value.includes('Subscriptions')) {
        return `<span class="font-mono text-sm" title="${value}">${value}</span>`;
      }
      return `<span class="font-mono" title="${value}">${value}</span>`;
    },

    retainFormatter(row, cell, value, columnDef, dataContext) {
      // For group rows, return empty string to let SlickGrid handle group row rendering
      if (dataContext && dataContext.__group) {
        return '';
      }
      return value ? 'Yes' : 'No';
    },

    dataSizeFormatter(row, cell, value, columnDef, dataContext) {
      const payloadSize = dataContext.payload_size || 0;
      const dataSize = dataContext.data_size || 0;

      // Show format: payload_size/data_size (no units)
      if (payloadSize === 0 && dataSize === 0) {
        return '-';
      } else if (payloadSize === dataSize) {
        // If they're the same, just show one value
        return dataSize.toString();
      } else {
        return `${payloadSize}/${dataSize}`;
      }
    },

    getTypeBadgeClass(type) {
      switch (type) {
        case 'PUBLISH':
        case 'PUBACK':
        case 'PUBREC':
        case 'PUBREL':
        case 'PUBCOMP':
          return 'badge-info';
        case 'SUBSCRIBE':
        case 'SUBACK':
        case 'UNSUBSCRIBE':
        case 'UNSUBACK':
          return 'badge-accent';
        case 'CONNECT':
        case 'CONNACK':
          return 'badge-success';
        case 'DISCONNECT':
          return 'badge-warning';
        case 'PINGREQ':
        case 'PINGRESP':
          return 'badge-secondary';
        case 'AUTH':
          return 'badge-primary';
        default:
          return 'badge-ghost';
      }
    },



    // Check if a message is a failed ACK
    isFailedAckMessage(item) {
      const ackTypes = ['PUBACK', 'PUBREC', 'PUBREL', 'PUBCOMP', 'SUBACK', 'UNSUBACK', 'CONNACK', 'DISCONNECT'];

      if (!ackTypes.includes(item.type)) {
        return false;
      }

      // Check reason code for failure
      const reasonCode = item.reason_code;

      if (item.type === 'CONNACK') {
        // For CONNACK, 0 means success, anything else is failure
        return reasonCode !== 0;
      } else if (item.type === 'SUBACK' || item.type === 'UNSUBACK') {
        // For SUBACK/UNSUBACK, reason_code is an array, check if any failed
        if (Array.isArray(reasonCode)) {
          return reasonCode.some(code => code !== 0);
        }
        return reasonCode !== 0;
      } else if (item.type === 'DISCONNECT') {
        // For DISCONNECT, 0 and 1 are normal, others are errors
        return reasonCode !== 0 && reasonCode !== 1;
      } else {
        // For PUBACK, PUBREC, PUBREL, PUBCOMP: 0 means success, anything else is failure
        return reasonCode !== 0;
      }
    },

    // Export functionality
    handleExportEvent(data) {
      console.debug('Handling export event:', data);
      const format = data.format;

      if (format === 'csv') {
        this.exportToCSV();
      } else if (format === 'excel') {
        this.exportToExcel();
      } else if (format === 'json') {
        this.exportToJSON();
      } else {
        console.warn('Unknown export format:', format);
      }
    },

    getExportData() {
      console.debug('=== Export Data Debug ===');
      console.debug('DataView available:', !!this.dataView);

      if (!this.dataView) {
        console.warn('DataView not available');
        return [];
      }

      // Get filtered data from the DataView
      const items = [];
      const length = this.dataView.getLength();
      console.debug('DataView length:', length);

      // Try to get items using getLength/getItem approach
      for (let i = 0; i < length; i++) {
        const item = this.dataView.getItem(i);
        // Skip group headers
        if (item && !item.__group) {
          items.push(item);
        }
      }

      console.debug(`Found ${items.length} items using getItem approach`);

      // If no items found, try alternative approaches
      if (items.length === 0) {
        console.debug('No items found with getItem, trying alternative approaches...');

        // Try to get all items directly from DataView
        if (this.dataView.getItems) {
          const allItems = this.dataView.getItems();
          console.debug('DataView.getItems() returned:', allItems ? allItems.length : 'null', 'items');
          if (allItems && allItems.length > 0) {
            // Filter out group headers and apply current filters
            allItems.forEach(item => {
              if (item && !item.__group) {
                // Apply the same filter logic as the grid
                if (this.dataView._hookFilters && this.dataView._hookInstance) {
                  const filterFunction = this.dataView.getFilter();
                  if (!filterFunction || filterFunction.call(this.dataView, item)) {
                    items.push(item);
                  }
                } else {
                  items.push(item);
                }
              }
            });
          }
        }

        console.debug(`Found ${items.length} items using getItems approach`);
      }

      // If still no items, try to get from the original data source
      if (items.length === 0) {
        console.debug('Still no items found, checking grid element data...');
        const gridDataJson = this.el.dataset.gridData;
        if (gridDataJson) {
          try {
            const gridData = JSON.parse(gridDataJson);
            console.debug('Grid data from element:', gridData ? gridData.length : 'null', 'items');
            if (Array.isArray(gridData) && gridData.length > 0) {
              // Apply current filters to the raw data
              gridData.forEach(item => {
                if (item && !item.__group) {
                  // Apply the same filter logic as the grid
                  if (this.filterFunction && this.filterFunction.call(this, item)) {
                    items.push(item);
                  } else if (!this.filterFunction) {
                    items.push(item);
                  }
                }
              });
            }
          } catch (error) {
            console.error('Error parsing grid data:', error);
          }
        }

        console.debug(`Found ${items.length} items using raw data approach`);
      }

      console.debug(`Final export data: ${items.length} items`);
      console.debug('=== End Export Data Debug ===');
      return items;
    },

    formatExportRow(item) {
      // Format timestamp for export
      const timestamp = this.formatTimestampForExport(item.timestamp);

      // Extract topic
      let topic = 'N/A';
      if (typeof item.topic === 'string' && item.topic) {
        topic = item.topic;
      } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
        const firstTopic = item.topics[0];
        if (typeof firstTopic === 'object' && firstTopic.topic) {
          topic = firstTopic.topic;
        } else if (typeof firstTopic === 'string') {
          topic = firstTopic;
        }
      }

      // Format properties as JSON string
      const properties = item.properties ? JSON.stringify(item.properties) : '';

      return {
        timestamp: timestamp,
        type: item.type || 'N/A',
        direction: item.direction || 'N/A',
        client_id: item.client_id || 'unknown',
        topic: topic,
        payload: item.payload || '',
        payload_size: item.payload_size || 0,
        data_size: item.data_size || 0,
        retain: item.retain ? 'Yes' : 'No',
        packet_id: item.packet_id || 'N/A',
        properties: properties
      };
    },

    formatTimestampForExport(timestamp) {
      if (!timestamp) return 'N/A';
      try {
        // Convert microseconds to milliseconds
        const date = new Date(timestamp / 1000);

        // Return RFC3339 format (ISO 8601 with timezone)
        return date.toISOString();
      } catch (error) {
        console.error('Error formatting timestamp for export:', error);
        return 'Invalid Date';
      }
    },

    exportToCSV() {
      try {
        console.log('Starting CSV export...');
        const data = this.getExportData();
        console.log('Export data retrieved:', data.length, 'items');

        if (data.length === 0) {
          console.warn('No data available for export');
          alert('No data to export. Please ensure there are trace messages visible in the grid.');
          return;
        }

        // Define CSV headers
        const headers = [
          'Timestamp',
          'Type',
          'Direction',
          'Client ID',
          'Topic',
          'Payload',
          'Payload Size',
          'Data Size',
          'Retain',
          'Packet ID',
          'Properties'
        ];

        // Format data for CSV
        const csvRows = [headers];
        data.forEach(item => {
          const row = this.formatExportRow(item);
          csvRows.push([
            this.escapeCsvValue(row.timestamp),
            this.escapeCsvValue(row.type),
            this.escapeCsvValue(row.direction),
            this.escapeCsvValue(row.client_id),
            this.escapeCsvValue(row.topic),
            this.escapeCsvValue(row.payload),
            this.escapeCsvValue(row.payload_size),
            this.escapeCsvValue(row.data_size),
            this.escapeCsvValue(row.retain),
            this.escapeCsvValue(row.packet_id),
            this.escapeCsvValue(row.properties)
          ]);
        });

        // Convert to CSV string
        const csvContent = csvRows.map(row => row.join(',')).join('\n');

        // Generate filename with broker name
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[T:]/g, '_');
        const brokerName = this.getBrokerName();
        const filename = `mqtt_trace_export_${brokerName}_${timestamp}.csv`;

        // Download file
        this.downloadFile(csvContent, filename, 'text/csv');

        console.log(`Exported ${data.length} rows to CSV`);
      } catch (error) {
        console.error('Error exporting to CSV:', error);
        alert('Failed to export CSV file');
      }
    },

    exportToExcel() {
      try {
        console.log('Starting Excel export...');
        const data = this.getExportData();
        console.log('Export data retrieved:', data.length, 'items');

        if (data.length === 0) {
          console.warn('No data available for export');
          alert('No data to export. Please ensure there are trace messages visible in the grid.');
          return;
        }

        // Generate Excel XML content
        const excelContent = this.generateExcelXML(data);

        // Generate filename with broker name
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[T:]/g, '_');
        const brokerName = this.getBrokerName();
        const filename = `mqtt_trace_export_${brokerName}_${timestamp}.xlsx`;

        // Download file
        this.downloadFile(excelContent, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        console.log(`Exported ${data.length} rows to Excel`);
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        alert('Failed to export Excel file');
      }
    },

    escapeCsvValue(value) {
      if (value === null || value === undefined) return '';
      const stringValue = String(value);
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return '"' + stringValue.replace(/"/g, '""') + '"';
      }
      return stringValue;
    },

    exportToJSON() {
      try {
        console.log('Starting JSON export...');
        const data = this.getExportData();
        console.log('Export data retrieved:', data.length, 'items');

        if (data.length === 0) {
          console.warn('No data available for export');
          alert('No data to export. Please ensure there are trace messages visible in the grid.');
          return;
        }

        // Format data for JSON export
        const jsonData = data.map(item => ({
          timestamp: this.formatTimestampForExport(item.timestamp),
          type: item.type || 'N/A',
          direction: item.direction || 'N/A',
          client_id: item.client_id || 'unknown',
          topic: this.formatTopicForExport(item.topic || item.topics),
          payload: item.payload || '',
          payload_size: item.payload_size || 0,
          data_size: item.data_size || 0,
          retain: item.retain || false,
          packet_id: item.packet_id === "undefined" ? null : item.packet_id,
          properties: item.properties || {}
        }));

        // Convert to JSON string with pretty formatting
        const jsonContent = JSON.stringify(jsonData, null, 2);

        // Generate filename with broker name
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[T:]/g, '_');
        const brokerName = this.getBrokerName();
        const filename = `mqtt_trace_export_${brokerName}_${timestamp}.json`;

        // Download file
        this.downloadFile(jsonContent, filename, 'application/json');

        console.log(`Exported ${data.length} rows to JSON`);
      } catch (error) {
        console.error('Error exporting to JSON:', error);
        alert('Failed to export JSON file');
      }
    },

    formatTopicForExport(topicData) {
      if (!topicData) return 'N/A';

      // Handle serialized topic format
      if (Array.isArray(topicData)) {
        return topicData.map(t => t.topic || t).join(', ');
      }

      // Handle string format
      if (typeof topicData === 'string') {
        return topicData;
      }

      return 'N/A';
    },

    generateExcelXML(data) {
      // Simple Excel XML format
      let xml = '<?xml version="1.0"?>\n';
      xml += '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" ';
      xml += 'xmlns:o="urn:schemas-microsoft-com:office:office" ';
      xml += 'xmlns:x="urn:schemas-microsoft-com:office:excel" ';
      xml += 'xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" ';
      xml += 'xmlns:html="http://www.w3.org/TR/REC-html40">\n';
      xml += '<Worksheet ss:Name="MQTT Trace">\n';
      xml += '<Table>\n';

      // Add header row
      xml += '<Row>\n';
      const headers = ['Timestamp', 'Type', 'Direction', 'Client ID', 'Topic', 'Payload', 'Payload Size', 'Data Size', 'Retain', 'Packet ID', 'Properties'];
      headers.forEach(header => {
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(header)}</Data></Cell>\n`;
      });
      xml += '</Row>\n';

      // Add data rows
      data.forEach(item => {
        const row = this.formatExportRow(item);
        xml += '<Row>\n';
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.timestamp)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.type)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.direction)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.client_id)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.topic)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.payload)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="Number">${row.payload_size}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="Number">${row.data_size}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.retain)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.packet_id)}</Data></Cell>\n`;
        xml += `<Cell><Data ss:Type="String">${this.escapeXml(row.properties)}</Data></Cell>\n`;
        xml += '</Row>\n';
      });

      xml += '</Table>\n';
      xml += '</Worksheet>\n';
      xml += '</Workbook>';

      return xml;
    },

    escapeXml(value) {
      if (value === null || value === undefined) return '';
      return String(value)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    },

    getBrokerName() {
      // Try to get broker name from the page context
      // Look for broker tab or other indicators
      const activeBrokerTab = document.querySelector('.tab.tab-active');
      if (activeBrokerTab) {
        const brokerText = activeBrokerTab.textContent.trim();
        // Extract broker name from text like "websocket MQTT" or "websocket ws://broker.emqx.io:8083"
        if (brokerText.includes('websocket')) {
          const parts = brokerText.split(' ');
          if (parts.length > 1) {
            // If it's a URL, extract the hostname
            const brokerPart = parts[1];
            if (brokerPart.includes('://')) {
              try {
                const url = new URL(brokerPart);
                return url.hostname.replace(/\./g, '_');
              } catch (e) {
                return brokerPart.replace(/[^a-zA-Z0-9]/g, '_');
              }
            } else {
              return brokerPart.replace(/[^a-zA-Z0-9]/g, '_');
            }
          }
        }
        return brokerText.replace(/[^a-zA-Z0-9]/g, '_');
      }

      // Fallback: try to get from URL or other sources
      const pathParts = window.location.pathname.split('/');
      if (pathParts.length > 1 && pathParts[1]) {
        return pathParts[1].replace(/[^a-zA-Z0-9]/g, '_');
      }

      // Default fallback
      return 'mqtt_broker';
    },

    downloadFile(content, filename, mimeType) {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      setTimeout(() => URL.revokeObjectURL(url), 100);
    }
  }
};

const liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// Add custom event handlers
window.addEventListener("phx:focus_element", (e) => {
  const element = document.getElementById(e.detail.id);
  if (element) {
    setTimeout(() => {
      element.focus();
    }, 50);
  }
});

// Global template insertion handler
window.addEventListener("phx:insert_template_global", (e) => {
  const { target_id, text } = e.detail;
  console.log('Global insert_template_global event received:', { target_id, text });

  // First try to find the exact target_id
  let textarea = document.getElementById(target_id);

  // If not found, try to find any textarea in UnifiedPayloadEditorComponent containers
  if (!textarea) {
    console.log(`Exact textarea ${target_id} not found, searching for UnifiedPayloadEditor textareas...`);

    // Look for any textarea with id starting with "payload-editor-" in the document
    const allTextareas = document.querySelectorAll('textarea[id^="payload-editor-"]');
    console.log(`Found ${allTextareas.length} payload editor textareas:`, Array.from(allTextareas).map(t => t.id));

    // Find the one that's currently visible (in a modal or active component)
    for (const ta of allTextareas) {
      const rect = ta.getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        textarea = ta;
        console.log(`Using visible textarea: ${ta.id}`);
        break;
      }
    }
  }

  if (!textarea) {
    console.log(`No suitable textarea found, ignoring event`);
    return;
  }

  // Insert text at cursor position
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const currentValue = textarea.value;

  const newValue = currentValue.substring(0, start) + text + currentValue.substring(end);
  textarea.value = newValue;

  const newCursorPos = start + text.length;
  textarea.setSelectionRange(newCursorPos, newCursorPos);
  textarea.focus();

  // Trigger change event to update LiveView
  textarea.dispatchEvent(new Event('input', { bubbles: true }));
  textarea.dispatchEvent(new Event('change', { bubbles: true }));

  console.log(`Successfully inserted template into ${textarea.id}`);
});

// Auto-dismiss flash messages
window.addEventListener("phx:auto_dismiss_flash", (e) => {
  const { kind, delay } = e.detail;
  const flashElement = document.getElementById(`flash-${kind}`);

  if (flashElement) {
    setTimeout(() => {
      // Check if the flash element still exists and is visible
      if (flashElement && flashElement.style.display !== 'none') {
        // Trigger the same action as clicking the close button
        const closeButton = flashElement.querySelector('button[phx-click="lv:clear-flash"]');
        if (closeButton) {
          closeButton.click();
        } else {
          // Fallback: hide the element directly
          flashElement.style.display = 'none';
        }
      }
    }, delay || 5000);
  }
});

// Update certificate files visibility when modal is shown
window.addEventListener("phx:show", (_e) => {
  // Wait a bit for the DOM to be updated
  setTimeout(() => {
    window.updateCertificateFilesVisibility();
  }, 100);
});



// Initialize functionality
document.addEventListener("DOMContentLoaded", () => {
  // Initialize certificate files visibility
  window.updateCertificateFilesVisibility();
});



// connect if there are any LiveViews on the page
liveSocket.connect()

window.liveSocket = liveSocket

// Global function to manually restore group states
window.restoreGroupStates = function() {
  console.log('Global restoreGroupStates called');
  const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
  if (traceGridElement && traceGridElement.__phoenixHook) {
    console.log('Found TraceSlickGrid hook, calling restoreGroupStates');
    traceGridElement.__phoenixHook.restoreGroupStates();
  } else {
    console.log('TraceSlickGrid hook not found');
  }
}

// Global function to check grouping status
window.checkGroupingStatus = function() {
  console.log('Global checkGroupingStatus called');
  const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
  if (traceGridElement && traceGridElement.__phoenixHook) {
    const hook = traceGridElement.__phoenixHook;
    console.log('=== GROUPING STATUS ===');
    console.log('this.groupingEnabled:', hook.groupingEnabled);
    console.log('this.filters.topic_grouping_enabled:', hook.filters ? hook.filters.topic_grouping_enabled : 'filters not available');
    console.log('data-topic-grouping-enabled:', traceGridElement.dataset.topicGroupingEnabled);
    console.log('Stored group states count:', hook.groupExpandStates ? hook.groupExpandStates.size : 'not available');
    console.log('DataView available:', !!hook.dataView);
    if (hook.dataView) {
      const groups = hook.dataView.getGroups();
      console.log('Current groups count:', groups ? groups.length : 'no groups');
    }
    console.log('======================');
  } else {
    console.log('TraceSlickGrid hook not found');
  }
}

// Global function to manually trigger group state capture on change
window.captureGroupStatesOnChange = function(groupingKey, isExpanded) {
  console.log('Global captureGroupStatesOnChange called');
  const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
  if (traceGridElement && traceGridElement.__phoenixHook) {
    console.log('Found TraceSlickGrid hook, calling captureGroupStatesOnChange');
    traceGridElement.__phoenixHook.captureGroupStatesOnChange(groupingKey, isExpanded);
  } else {
    console.log('TraceSlickGrid hook not found');
  }
}

// Function to update certificate files visibility based on selected certificate type
window.updateCertificateFilesVisibility = function() {
  const certificateFilesSection = document.getElementById('certificate-files-section');
  if (!certificateFilesSection) return;

  const selfSignedRadio = document.getElementById('certificate-type-self-signed');
  if (selfSignedRadio && selfSignedRadio.checked) {
    certificateFilesSection.style.display = 'block';
  } else {
    certificateFilesSection.style.display = 'none';
  }
}

// The lines below enable quality of life phoenix_live_reload
// development features:
//
//     1. stream server logs to the browser console
//     2. click on elements to jump to their definitions in your code editor
//
if (process.env.NODE_ENV === "development") {
  window.addEventListener("phx:live_reload:attached", ({detail: reloader}) => {
    // Enable server log streaming to client.
    // Disable with reloader.disableServerLogs()
    reloader.enableServerLogs()

    // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
    //
    //   * click with "c" key pressed to open at caller location
    //   * click with "d" key pressed to open at function component definition location
    let keyDown
    window.addEventListener("keydown", e => keyDown = e.key)
    window.addEventListener("keyup", _e => keyDown = null)
    window.addEventListener("click", e => {
      if(keyDown === "c"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtCaller(e.target)
      } else if(keyDown === "d"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtDef(e.target)
      }
    }, true)

    window.liveReloader = reloader
  })
}

